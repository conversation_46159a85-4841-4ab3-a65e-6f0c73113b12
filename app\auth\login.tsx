import { Ionicons } from '@expo/vector-icons';
import { Link } from 'expo-router';
import React, { useState } from 'react';
import {
	KeyboardAvoidingView,
	Platform,
	SafeAreaView,
	Text,
	TextInput,
	TouchableOpacity,
	View,
} from 'react-native';

const Login = () => {
	const [email, setEmail] = useState('');
	const [phoneNumber, setPhoneNumber] = useState('');
	const [password, setPassword] = useState('');
	const [showPassword, setShowPassword] = useState(false);
	const [isEmailMode, setIsEmailMode] = useState(false); // Toggle between phone and email
	const [countryCode, setCountryCode] = useState('+234');

	const handleLogin = () => {
		// TODO: Implement login logic
		const loginData = isEmailMode
			? { email, password }
			: { phoneNumber: countryCode + phoneNumber, password };
		console.log('Login pressed', loginData);
	};

	const toggleInputMode = () => {
		setIsEmailMode(!isEmailMode);
		// Clear inputs when switching modes
		setEmail('');
		setPhoneNumber('');
	};

	return (
		<SafeAreaView className='flex-1 bg-white'>
			<KeyboardAvoidingView
				className='flex-1'
				behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
				{/* Header with back button and menu */}
				<View className='flex-row justify-between items-center px-6 pt-4 pb-8'>
					<TouchableOpacity>
						<Ionicons
							name='chevron-back'
							size={24}
							color='#000'
						/>
					</TouchableOpacity>
					<TouchableOpacity>
						<View className='w-8 h-8 rounded-full bg-red-500 items-center justify-center'>
							<View className='flex-row space-x-1'>
								<View className='w-1 h-1 bg-white rounded-full' />
								<View className='w-1 h-1 bg-white rounded-full' />
								<View className='w-1 h-1 bg-white rounded-full' />
							</View>
						</View>
					</TouchableOpacity>
				</View>

				<View className='flex-1 px-6'>
					{/* Welcome Header */}
					<View className='mb-12'>
						<Text className='text-gray-400 text-lg mb-1'>Welcome back,</Text>
						<Text className='text-black text-4xl font-bold mb-4'>Champ.</Text>
						<View className='flex-row'>
							<Text className='text-gray-400 text-base'>Login with </Text>
							<TouchableOpacity onPress={toggleInputMode}>
								<Text className='text-red-500 text-base'>
									{isEmailMode ? 'phone number' : 'email'}
								</Text>
							</TouchableOpacity>
							<Text className='text-gray-400 text-base'> instead.</Text>
						</View>
					</View>

					{/* Form */}
					<View className='mb-8'>
						{/* Phone Number or Email Input */}
						{isEmailMode ? (
							<View className='mb-6'>
								<Text className='text-gray-600 font-medium mb-3'>Email</Text>
								<TextInput
									className='bg-gray-50 border border-gray-200 rounded-xl px-4 py-4 text-black text-base'
									placeholder='Enter email address'
									placeholderTextColor='#9CA3AF'
									value={email}
									onChangeText={setEmail}
									keyboardType='email-address'
									autoCapitalize='none'
								/>
							</View>
						) : (
							<View className='mb-6'>
								<Text className='text-gray-600 font-medium mb-3'>
									Phone number
								</Text>
								<View className='flex-row bg-gray-50 border border-gray-200 rounded-xl overflow-hidden'>
									{/* Country Code Selector */}
									<View className='flex-row items-center px-4 py-4 border-r border-gray-200'>
										{/* Nigerian Flag */}
										<View className='w-6 h-4 mr-2 rounded overflow-hidden'>
											<View className='flex-1 bg-green-500' />
											<View className='flex-1 bg-white' />
											<View className='flex-1 bg-green-600' />
										</View>
										<Text className='text-black text-base mr-1'>
											{countryCode}
										</Text>
										<Ionicons
											name='chevron-down'
											size={16}
											color='#6B7280'
										/>
									</View>
									{/* Phone Input */}
									<TextInput
										className='flex-1 px-4 py-4 text-black text-base'
										placeholder='Enter phone number'
										placeholderTextColor='#9CA3AF'
										value={phoneNumber}
										onChangeText={setPhoneNumber}
										keyboardType='phone-pad'
									/>
								</View>
							</View>
						)}

						{/* Password Input */}
						<View className='mb-6'>
							<View className='flex-row justify-between items-center mb-3'>
								<Text className='text-gray-600 font-medium'>Password</Text>
								<TouchableOpacity>
									<Text className='text-red-500 font-medium'>
										Forget password
									</Text>
								</TouchableOpacity>
							</View>
							<View className='flex-row bg-gray-50 border border-gray-200 rounded-xl overflow-hidden'>
								<TextInput
									className='flex-1 px-4 py-4 text-black text-base'
									placeholder='Enter password'
									placeholderTextColor='#9CA3AF'
									value={password}
									onChangeText={setPassword}
									secureTextEntry={!showPassword}
								/>
								<TouchableOpacity
									className='px-4 py-4 justify-center'
									onPress={() => setShowPassword(!showPassword)}>
									<Ionicons
										name={showPassword ? 'eye-off' : 'eye'}
										size={20}
										color='#6B7280'
									/>
								</TouchableOpacity>
							</View>
						</View>

						{/* Login Button */}
						<TouchableOpacity
							className='bg-red-500 py-4 rounded-xl items-center mb-8'
							onPress={handleLogin}>
							<Text className='text-white font-semibold text-base'>Login</Text>
						</TouchableOpacity>
					</View>

					{/* Sign Up Link */}
					<View className='flex-row justify-center items-center'>
						<Text className='text-gray-400 text-base'>
							Don't have an Account?{' '}
						</Text>
						<Link
							href='/auth/register'
							asChild>
							<TouchableOpacity>
								<Text className='text-red-500 font-medium text-base'>
									Create
								</Text>
							</TouchableOpacity>
						</Link>
					</View>
				</View>
			</KeyboardAvoidingView>
		</SafeAreaView>
	);
};

export default Login;
