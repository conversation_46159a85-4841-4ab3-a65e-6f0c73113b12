import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

const ErrorComponent = ({ message = 'Enter a valid email' }) => {
	return (
		<View style={styles.container}>
			<View style={styles.iconContainer}>
				<Text style={styles.icon}>⚠</Text>
			</View>
			<View style={styles.textContainer}>
				<Text style={styles.title}>Error</Text>
				<Text style={styles.message}>{message}</Text>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		flexDirection: 'row',
		backgroundColor: '#fee2e2', // Light red background
		borderColor: '#fecaca', // Slightly darker red border
		borderWidth: 1,
		borderRadius: 12,
		padding: 16,
		alignItems: 'flex-start',
		marginHorizontal: 16,
		marginVertical: 8,
	},
	iconContainer: {
		width: 24,
		height: 24,
		borderRadius: 12,
		backgroundColor: '#ef4444', // Red background for icon
		justifyContent: 'center',
		alignItems: 'center',
		marginRight: 12,
		marginTop: 2,
	},
	icon: {
		color: '#ffffff',
		fontSize: 14,
		fontWeight: 'bold',
	},
	textContainer: {
		flex: 1,
	},
	title: {
		fontSize: 16,
		fontWeight: '600',
		color: '#1f2937', // Dark gray
		marginBottom: 4,
	},
	message: {
		fontSize: 14,
		color: '#6b7280', // Medium gray
		lineHeight: 20,
	},
});

export default ErrorComponent;
