import { router } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
	Dimensions,
	FlatList,
	Image,
	NativeScrollEvent,
	NativeSyntheticEvent,
	Pressable,
	SafeAreaView,
	StatusBar,
	Text,
	View,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width, height } = Dimensions.get('window');

interface OnboardingSlide {
	id: string;
	image: any;
	title: string;
	description: string;
}

const onboardingData: OnboardingSlide[] = [
	{
		id: '1',
		image: require('@/assets/images/slideOneImg.png'),
		title: 'Order anything you want on Camell',
		description:
			'Place orders in seconds, and get them delivered to your hostel—fast and secure.',
	},
	{
		id: '2',
		image: require('@/assets/images/slideTwoImg.png'),
		title: 'Affordable & fast campus delivery',
		description:
			'Place orders in seconds, and get them delivered to your hostel—fast and secure.',
	},
	{
		id: '3',
		image: require('@/assets/images/slideThreeImg.png'),
		title: 'Refer a friend & get paid',
		description:
			'Place orders in seconds, and get them delivered to your hostel—fast and secure.',
	},
];

export default function OnboardingScreen() {
	const [currentIndex, setCurrentIndex] = useState(0);
	const flatListRef = useRef<FlatList>(null);
	const autoPlayRef = useRef<ReturnType<typeof setInterval> | null>(null);
	const insets = useSafeAreaInsets(); // to respect top/bottom safe areas

	useEffect(() => {
		autoPlayRef.current = setInterval(() => {
			setCurrentIndex((prevIndex) => {
				const nextIndex = (prevIndex + 1) % onboardingData.length;
				flatListRef.current?.scrollToIndex({
					index: nextIndex,
					animated: true,
				});
				return nextIndex;
			});
		}, 3000);

		return () => {
			if (autoPlayRef.current) clearInterval(autoPlayRef.current);
		};
	}, []);

	const stopAutoPlay = () => {
		if (autoPlayRef.current) {
			clearInterval(autoPlayRef.current);
			autoPlayRef.current = null;
		}
	};

	const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
		stopAutoPlay();
		const index = Math.round(event.nativeEvent.contentOffset.x / width);
		setCurrentIndex(index);
	};

	const handleGetStarted = () => {
		stopAutoPlay();
		router.replace('/auth/register');
	};

	const handleLogin = () => {
		stopAutoPlay();
		router.replace('/auth/login');
	};

	const renderPagination = () => (
		<View className='flex-row justify-center items-center px-6'>
			<View className='flex-row w-[80%] justify-center'>
				{onboardingData.map((_, index) => (
					<View
						key={index}
						className={`h-[2px] flex-1 mx-1 rounded-full ${
							index === currentIndex ? 'bg-[#FF3B30]' : 'bg-gray-300'
						}`}
					/>
				))}
			</View>
		</View>
	);

	const renderSlide = ({ item }: { item: OnboardingSlide }) => (
		<View
			className='flex-1 justify-center px-6 relative'
			style={{ width }}>
			<Image
				source={item.image}
				className='w-full h-[450px]'
				resizeMode='contain'
			/>
			<View className='absolute bg-white bottom-10 pt-14 left-0 right-0 px-6'>
				<Text className='text-5xl font-extrabold text-black mt-8'>
					{item.title}
				</Text>
				<Text className='text-xl text-gray-500 mt-3 leading-6'>
					{item.description}
				</Text>
			</View>
		</View>
	);

	return (
		<SafeAreaView
			className='flex-1 bg-white'
			style={{ paddingTop: insets.top }}>
			<StatusBar
				barStyle='dark-content'
				backgroundColor='#FFFFFF'
			/>

			{/* Pagination at top */}
			<View className='pt-2'>{renderPagination()}</View>

			{/* Slides */}
			<FlatList
				ref={flatListRef}
				data={onboardingData}
				renderItem={renderSlide}
				horizontal
				pagingEnabled
				showsHorizontalScrollIndicator={false}
				onScroll={handleScroll}
				scrollEventThrottle={16}
				keyExtractor={(item) => item.id}
				onTouchStart={stopAutoPlay}
				onScrollBeginDrag={stopAutoPlay}
			/>

			{/* Bottom Buttons */}
			<View
				className='px-6 gap-6 mb-3'
				style={{ paddingBottom: insets.bottom + 20 }}>
				<Pressable
					onPress={handleGetStarted}
					className='bg-primary py-4 rounded-lg w-full'>
					<Text className='text-white font-bold text-center text-xl'>
						Get Started
					</Text>
				</Pressable>

				<Pressable
					onPress={handleLogin}
					className='bg-[#FFF5F5] py-4 w-full rounded-lg'>
					<Text className='text-primary font-bold text-center text-xl'>
						Login to Account
					</Text>
				</Pressable>
			</View>
		</SafeAreaView>
	);
}
